"""
Test script to debug LLM responses for palette evolution and explanation
"""

import sys
import os
import json

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_llm_direct():
    """Test LLM responses directly"""
    print("🧪 Testing LLM Responses Directly")
    print("=" * 40)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ LLM service initialized successfully")
        except ValueError as e:
            print(f"❌ Failed to initialize LLM service: {e}")
            return
        
        # Test 1: Simple evolution prompt
        print("\n🧬 Test 1: Palette Evolution")
        test_palette = ["#FF6B35", "#F7931E", "#FFD23F"]
        feedback = "make it more professional"
        
        # Create the prompt manually
        evolution_prompt = f"""You are a color expert. I need you to modify this color palette based on user feedback.

Current palette: {test_palette}
User feedback: {feedback}
Target mood: Not specified

CRITICAL: You must respond with <PERSON>LY a JSON object. No explanations, no markdown, no extra text.

Your response must be exactly this format:
{{"evolved_palette": ["#RRGGBB", "#RRGGBB", "#RRGGBB"], "changes_made": "description", "reasoning": "explanation"}}

Make the colors {feedback.lower()}. Keep the same number of colors ({len(test_palette)}).

JSON response:"""
        
        print(f"Prompt: {evolution_prompt}")
        
        try:
            # Test the LLM directly
            from langchain.schema import HumanMessage
            messages = [HumanMessage(content=evolution_prompt)]
            response = service.llm(messages)
            response_content = response.content.strip()
            
            print(f"Raw LLM Response: {response_content}")
            
            # Try to parse it
            try:
                cleaned = service._extract_and_clean_json(response_content)
                parsed = json.loads(cleaned)
                print(f"✅ Successfully parsed JSON: {parsed}")
            except Exception as parse_error:
                print(f"❌ Failed to parse JSON: {parse_error}")
                
        except Exception as llm_error:
            print(f"❌ LLM call failed: {llm_error}")
        
        # Test 2: Simple explanation prompt
        print("\n🎨 Test 2: Color Explanation")
        test_palette_2 = ["#2C3E50", "#3498DB"]
        context = "corporate app"
        
        explanation_prompt = f"""Analyze this color palette: {test_palette_2}
Context: {context}

CRITICAL: Respond with ONLY a JSON object. No explanations, no markdown, no extra text.

Your response must be exactly this format:
{{"palette_analysis": {{"overall_mood": "mood", "harmony_type": "harmony", "applications": ["app1", "app2"], "user_experience": "ux"}}, "color_explanations": [{{"color": "#RRGGBB", "name": "name", "psychology": "psych", "cultural": "culture", "design_use": "use", "emotion": "emotion"}}]}}

Analyze each color in the palette ({len(test_palette_2)} colors total).

JSON response:"""
        
        print(f"Prompt: {explanation_prompt}")
        
        try:
            # Test the LLM directly
            messages = [HumanMessage(content=explanation_prompt)]
            response = service.llm(messages)
            response_content = response.content.strip()
            
            print(f"Raw LLM Response: {response_content}")
            
            # Try to parse it
            try:
                cleaned = service._extract_and_clean_json(response_content)
                parsed = json.loads(cleaned)
                print(f"✅ Successfully parsed JSON: {parsed}")
            except Exception as parse_error:
                print(f"❌ Failed to parse JSON: {parse_error}")
                
        except Exception as llm_error:
            print(f"❌ LLM call failed: {llm_error}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_service_methods():
    """Test the service methods directly"""
    print("\n🔧 Testing Service Methods")
    print("=" * 30)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ Service initialized")
        except ValueError:
            print("❌ No API key, testing fallback methods only")
            service = TextToColorService.__new__(TextToColorService)
        
        # Test evolution method
        print("\n🧬 Testing evolve_palette method...")
        test_palette = ["#FF6B35", "#F7931E", "#FFD23F"]
        feedback = "make it more professional"
        
        if hasattr(service, 'evolve_palette'):
            result = service.evolve_palette(test_palette, feedback)
            print(f"Evolution result: {result}")
        else:
            result = service._fallback_palette_evolution(test_palette, feedback)
            print(f"Fallback evolution result: {result}")
        
        # Test explanation method
        print("\n🎨 Testing explain_colors method...")
        test_palette_2 = ["#2C3E50", "#3498DB"]
        context = "corporate app"
        
        if hasattr(service, 'explain_colors'):
            result = service.explain_colors(test_palette_2, context)
            print(f"Explanation result: {result}")
        else:
            result = service._fallback_color_explanation(test_palette_2, context)
            print(f"Fallback explanation result: {result}")
        
    except Exception as e:
        print(f"❌ Service test failed: {e}")

def test_json_extraction():
    """Test JSON extraction with various response formats"""
    print("\n🔍 Testing JSON Extraction")
    print("=" * 30)
    
    try:
        from backend.text_to_color import TextToColorService
        service = TextToColorService.__new__(TextToColorService)
        
        # Test cases with different response formats
        test_responses = [
            # Case 1: Clean JSON
            '{"evolved_palette": ["#FF5733", "#33FF57"], "changes_made": "test", "reasoning": "test"}',
            
            # Case 2: JSON with markdown
            '```json\n{"evolved_palette": ["#FF5733", "#33FF57"], "changes_made": "test", "reasoning": "test"}\n```',
            
            # Case 3: JSON with extra text
            'Here is the response:\n{"evolved_palette": ["#FF5733", "#33FF57"], "changes_made": "test", "reasoning": "test"}\nThat should work.',
            
            # Case 4: JSON with prefix
            'JSON response: {"evolved_palette": ["#FF5733", "#33FF57"], "changes_made": "test", "reasoning": "test"}',
            
            # Case 5: Malformed JSON
            'This is not JSON at all, just text.',
        ]
        
        for i, response in enumerate(test_responses, 1):
            print(f"\nTest Case {i}:")
            print(f"Input: {response}")
            
            try:
                cleaned = service._extract_and_clean_json(response)
                parsed = json.loads(cleaned)
                print(f"✅ Success: {parsed}")
            except Exception as e:
                print(f"❌ Failed: {e}")
        
    except Exception as e:
        print(f"❌ JSON extraction test failed: {e}")

def main():
    """Run all LLM response tests"""
    print("🚀 LLM Response Debug Test Suite")
    print("=" * 50)
    
    print("This script helps debug LLM response parsing issues.")
    print("It tests the LLM directly and checks JSON extraction.")
    
    # Run tests
    test_llm_direct()
    test_service_methods()
    test_json_extraction()
    
    print("\n📊 Debug Summary")
    print("=" * 20)
    print("If LLM responses are not JSON, check:")
    print("1. Model compatibility (deepseek-r1-distill-llama-70b)")
    print("2. Prompt clarity and format")
    print("3. API key and connection")
    print("4. Model's instruction-following capability")
    
    print("\n💡 Solutions:")
    print("• Try a different model (e.g., llama-3.1-70b-versatile)")
    print("• Simplify prompts further")
    print("• Add more explicit JSON format examples")
    print("• Use fallback methods if AI responses are unreliable")

if __name__ == '__main__':
    main()
