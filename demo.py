"""
Demo script for Color Extractor
Creates a sample image and extracts colors from it
"""

from PIL import Image, ImageDraw
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.color_extractor import ColorExtractor


def create_sample_image():
    """Create a sample image with multiple colors"""
    # Create a 400x400 image
    img = Image.new('RGB', (400, 400), 'white')
    draw = ImageDraw.Draw(img)
    
    # Draw colored rectangles
    colors = [
        (255, 0, 0),    # Red
        (0, 255, 0),    # Green
        (0, 0, 255),    # Blue
        (255, 255, 0),  # Yellow
        (255, 0, 255),  # <PERSON><PERSON>a
        (0, 255, 255),  # <PERSON><PERSON>
        (128, 128, 128), # <PERSON>
        (255, 165, 0),  # Orange
    ]
    
    # Draw rectangles in a 2x4 grid
    rect_width = 200
    rect_height = 100
    
    for i, color in enumerate(colors):
        x = (i % 2) * rect_width
        y = (i // 2) * rect_height
        draw.rectangle([x, y, x + rect_width, y + rect_height], fill=color)
    
    return img


def main():
    """Main demo function"""
    print("🎨 Color Extractor Demo")
    print("=" * 50)
    
    # Create color extractor
    extractor = ColorExtractor()
    
    # Create sample image
    print("Creating sample image...")
    sample_image = create_sample_image()
    sample_image.save('sample_image.png')
    print("✅ Sample image saved as 'sample_image.png'")
    
    # Extract colors
    print("\nExtracting dominant colors...")
    colors = extractor.extract_colors(sample_image, n_colors=8)
    
    print(f"\n🎯 Extracted {len(colors)} dominant colors:")
    print("-" * 40)
    
    for i, (r, g, b) in enumerate(colors, 1):
        hex_color = extractor.rgb_to_hex((r, g, b))
        print(f"{i:2d}. RGB({r:3d}, {g:3d}, {b:3d}) → {hex_color}")
    
    # Extract colors with percentages
    print("\nExtracting colors with percentages...")
    color_percentages = extractor.get_color_percentages(sample_image, n_colors=5)
    
    print(f"\n📊 Top {len(color_percentages)} colors with percentages:")
    print("-" * 50)
    
    for i, ((r, g, b), percentage) in enumerate(color_percentages, 1):
        hex_color = extractor.rgb_to_hex((r, g, b))
        print(f"{i}. RGB({r:3d}, {g:3d}, {b:3d}) → {hex_color} ({percentage:5.1f}%)")
    
    print("\n✨ Demo completed successfully!")
    print("\nTo run the full application:")
    print("1. Install dependencies: pip install -r requirements.txt")
    print("2. Start the backend: python backend/app.py")
    print("3. Open frontend/index.html in your browser")


if __name__ == '__main__':
    main()
