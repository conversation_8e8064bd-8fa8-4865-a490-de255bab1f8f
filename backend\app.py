"""
Flask API for Color Extraction Service
Provides endpoints for extracting dominant colors from uploaded images
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
from color_extractor import ColorExtractor
import io
from PIL import Image
import traceback

app = Flask(__name__)
CORS(app)  # Enable CORS for frontend communication

# Initialize color extractor
color_extractor = ColorExtractor()

@app.route('/', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Color Extraction API is running',
        'version': '1.0.0'
    })

@app.route('/extract-colors', methods=['POST'])
def extract_colors():
    """
    Extract dominant colors from uploaded image
    
    Expected form data:
    - image: Image file
    - n_colors: Number of colors to extract (optional, default: 5)
    - include_percentages: Whether to include color percentages (optional, default: false)
    
    Returns:
    JSON response with extracted colors
    """
    try:
        # Check if image file is present
        if 'image' not in request.files:
            return jsonify({
                'error': 'No image file provided',
                'message': 'Please upload an image file'
            }), 400
        
        image_file = request.files['image']
        
        # Check if file is selected
        if image_file.filename == '':
            return jsonify({
                'error': 'No image file selected',
                'message': 'Please select an image file'
            }), 400
        
        # Get optional parameters
        n_colors = int(request.form.get('n_colors', 5))
        include_percentages = request.form.get('include_percentages', 'false').lower() == 'true'
        
        # Validate n_colors
        if n_colors < 1 or n_colors > 20:
            return jsonify({
                'error': 'Invalid number of colors',
                'message': 'Number of colors must be between 1 and 20'
            }), 400
        
        # Read image data
        image_data = image_file.read()
        
        # Validate image format
        try:
            Image.open(io.BytesIO(image_data))
        except Exception:
            return jsonify({
                'error': 'Invalid image format',
                'message': 'Please upload a valid image file (JPEG, PNG, etc.)'
            }), 400
        
        # Extract colors
        if include_percentages:
            color_data = color_extractor.get_color_percentages(image_data, n_colors)
            colors = []
            for (r, g, b), percentage in color_data:
                colors.append({
                    'rgb': [r, g, b],
                    'hex': color_extractor.rgb_to_hex((r, g, b)),
                    'percentage': percentage
                })
        else:
            color_data = color_extractor.extract_colors(image_data, n_colors)
            colors = []
            for r, g, b in color_data:
                colors.append({
                    'rgb': [r, g, b],
                    'hex': color_extractor.rgb_to_hex((r, g, b))
                })
        
        return jsonify({
            'success': True,
            'colors': colors,
            'n_colors': len(colors),
            'include_percentages': include_percentages
        })
    
    except Exception as e:
        # Log the error for debugging
        print(f"Error processing image: {str(e)}")
        print(traceback.format_exc())
        
        return jsonify({
            'error': 'Internal server error',
            'message': 'An error occurred while processing the image'
        }), 500

@app.route('/extract-colors-batch', methods=['POST'])
def extract_colors_batch():
    """
    Extract colors from multiple images
    
    Expected form data:
    - images: Multiple image files
    - n_colors: Number of colors to extract per image (optional, default: 5)
    
    Returns:
    JSON response with extracted colors for each image
    """
    try:
        # Check if image files are present
        if 'images' not in request.files:
            return jsonify({
                'error': 'No image files provided',
                'message': 'Please upload image files'
            }), 400
        
        image_files = request.files.getlist('images')
        
        if not image_files or all(f.filename == '' for f in image_files):
            return jsonify({
                'error': 'No image files selected',
                'message': 'Please select image files'
            }), 400
        
        # Get optional parameters
        n_colors = int(request.form.get('n_colors', 5))
        
        # Validate n_colors
        if n_colors < 1 or n_colors > 20:
            return jsonify({
                'error': 'Invalid number of colors',
                'message': 'Number of colors must be between 1 and 20'
            }), 400
        
        results = []
        
        for i, image_file in enumerate(image_files):
            if image_file.filename == '':
                continue
                
            try:
                # Read image data
                image_data = image_file.read()
                
                # Validate image format
                Image.open(io.BytesIO(image_data))
                
                # Extract colors
                color_data = color_extractor.extract_colors(image_data, n_colors)
                colors = []
                for r, g, b in color_data:
                    colors.append({
                        'rgb': [r, g, b],
                        'hex': color_extractor.rgb_to_hex((r, g, b))
                    })
                
                results.append({
                    'filename': image_file.filename,
                    'index': i,
                    'success': True,
                    'colors': colors,
                    'n_colors': len(colors)
                })
                
            except Exception as e:
                results.append({
                    'filename': image_file.filename,
                    'index': i,
                    'success': False,
                    'error': f'Error processing image: {str(e)}'
                })
        
        return jsonify({
            'success': True,
            'results': results,
            'total_images': len(results)
        })
    
    except Exception as e:
        print(f"Error in batch processing: {str(e)}")
        print(traceback.format_exc())
        
        return jsonify({
            'error': 'Internal server error',
            'message': 'An error occurred while processing the images'
        }), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
