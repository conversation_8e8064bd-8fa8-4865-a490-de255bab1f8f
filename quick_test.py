"""
Quick test for the improved palette evolution and explanation features
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_evolution():
    """Test palette evolution with improved error handling"""
    print("🧬 Testing Improved Palette Evolution")
    print("=" * 40)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ AI service available")
        except ValueError:
            print("⚠️  No API key, testing fallback only")
            service = TextToColorService.__new__(TextToColorService)
            result = service._fallback_palette_evolution(
                ["#FF6B35", "#F7931E", "#FFD23F"], 
                "make it more professional"
            )
            print(f"Fallback result: {result}")
            return
        
        # Test evolution
        test_palette = ["#FF6B35", "#F7931E", "#FFD23F"]
        feedback = "make it more professional and muted"
        
        print(f"Original palette: {test_palette}")
        print(f"Feedback: {feedback}")
        
        result = service.evolve_palette(test_palette, feedback)
        
        if result['success']:
            print(f"✅ Evolution successful!")
            print(f"   Evolved palette: {result['evolved_palette']}")
            print(f"   Changes: {result['changes_made']}")
            print(f"   Method: {result['method']}")
        else:
            print(f"❌ Evolution failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def test_explanation():
    """Test color explanation with improved error handling"""
    print("\n🎨 Testing Improved Color Explanation")
    print("=" * 40)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ AI service available")
        except ValueError:
            print("⚠️  No API key, testing fallback only")
            service = TextToColorService.__new__(TextToColorService)
            result = service._fallback_color_explanation(
                ["#2C3E50", "#3498DB"], 
                "corporate app"
            )
            print(f"Fallback result: {result}")
            return
        
        # Test explanation
        test_palette = ["#2C3E50", "#3498DB"]
        context = "professional finance application"
        
        print(f"Palette: {test_palette}")
        print(f"Context: {context}")
        
        result = service.explain_colors(test_palette, context)
        
        if result['success']:
            print(f"✅ Explanation successful!")
            print(f"   Overall mood: {result['palette_analysis']['overall_mood']}")
            print(f"   Applications: {result['palette_analysis']['applications']}")
            print(f"   Method: {result['method']}")
            
            if result['color_explanations']:
                first_color = result['color_explanations'][0]
                print(f"   Example - {first_color['color']}: {first_color['psychology']}")
        else:
            print(f"❌ Explanation failed: {result.get('error', 'Unknown error')}")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")

def main():
    """Run quick tests"""
    print("🚀 Quick Test for Improved Features")
    print("=" * 45)
    
    print("Testing the improved LLM response handling...")
    print("• Switched to llama-3.1-70b-versatile model")
    print("• Simplified prompts for better JSON compliance")
    print("• Added fallback extraction for partial responses")
    print("• Enhanced error handling and debugging")
    
    test_evolution()
    test_explanation()
    
    print("\n✅ Quick test completed!")
    print("\n💡 If you're still seeing 'No JSON object found' errors:")
    print("1. Check your Groq API key is set correctly")
    print("2. The fallback methods should still work")
    print("3. Try running the Flask server and test via the web interface")
    print("4. The system gracefully falls back to algorithmic methods")

if __name__ == '__main__':
    main()
