"""
Test script for text-to-color functionality
Tests both the fallback method and API integration
"""

import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def test_fallback_method():
    """Test the fallback color extraction method"""
    print("🧪 Testing Fallback Color Extraction Method")
    print("=" * 50)
    
    from backend.text_to_color import TextToColorService
    
    # Create a temporary service instance for testing fallback
    temp_service = TextToColorService.__new__(TextToColorService)
    
    test_descriptions = [
        "tropical sunset beach vacation",
        "elegant luxury brand for jewelry", 
        "energetic fitness app for young athletes",
        "calm meditation and wellness center",
        "modern tech startup with innovation focus",
        "autumn forest with golden leaves",
        "deep ocean underwater scene",
        "cozy winter cabin fireplace",
        "vibrant spring garden flowers",
        "minimalist scandinavian design"
    ]
    
    print("Testing fallback color extraction:")
    for description in test_descriptions:
        color = temp_service._fallback_color_extraction(description)
        print(f"  📝 '{description}' → {color}")
    
    print("\n✅ Fallback method test completed!")

def test_palette_generation():
    """Test palette generation from seed colors"""
    print("\n🎨 Testing Palette Generation")
    print("=" * 50)
    
    from backend.generate_color import generate_smart_palette, generate_palette
    
    test_colors = [
        "#FF6B35",  # Orange
        "#3498DB",  # Blue  
        "#27AE60",  # Green
        "#8E44AD",  # Purple
        "#E74C3C"   # Red
    ]
    
    modes = ['smart', 'analogous', 'split_complementary', 'triadic', 'tetradic']
    
    for color in test_colors[:2]:  # Test first 2 colors
        print(f"\n🎯 Seed Color: {color}")
        for mode in modes:
            if mode == 'smart':
                palette = generate_smart_palette(color, 5)
            else:
                palette = generate_palette(color, mode, 5)
            print(f"  {mode:20} → {', '.join(palette)}")
    
    print("\n✅ Palette generation test completed!")

def test_api_integration():
    """Test API integration (requires running Flask server)"""
    print("\n🌐 Testing API Integration")
    print("=" * 50)
    
    import requests
    import json
    
    api_base = "http://localhost:5000"
    
    # Test health check
    try:
        response = requests.get(f"{api_base}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ API Health Check: {data['status']}")
            print(f"   Features: {data.get('features', {})}")
        else:
            print(f"❌ API Health Check failed: {response.status_code}")
            return
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to API. Make sure Flask server is running on localhost:5000")
        return
    
    # Test text-to-color endpoint
    test_descriptions = [
        "tropical sunset beach vacation",
        "elegant luxury brand for jewelry"
    ]
    
    for description in test_descriptions:
        try:
            response = requests.post(f"{api_base}/text-to-color", 
                                   json={"text_description": description})
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Text-to-Color: '{description}' → {data['seed_color']} ({data['method']})")
            else:
                print(f"❌ Text-to-Color failed: {response.status_code}")
        except Exception as e:
            print(f"❌ Error testing text-to-color: {e}")
    
    # Test palette generation endpoint
    try:
        response = requests.post(f"{api_base}/generate-palette-from-text", 
                               json={
                                   "text_description": "tropical sunset beach vacation",
                                   "palette_mode": "smart",
                                   "num_colors": 5
                               })
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Palette Generation: {len(data['palette'])} colors generated")
            print(f"   Seed: {data['seed_color']}")
            print(f"   Palette: {', '.join([c['hex'] for c in data['palette']])}")
        else:
            print(f"❌ Palette generation failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Error testing palette generation: {e}")
    
    print("\n✅ API integration test completed!")

def main():
    """Run all tests"""
    print("🚀 Text-to-Color System Test Suite")
    print("=" * 60)
    
    # Test 1: Fallback method (always works)
    test_fallback_method()
    
    # Test 2: Palette generation (always works)
    test_palette_generation()
    
    # Test 3: API integration (requires running server)
    test_api_integration()
    
    print("\n🎉 All tests completed!")
    print("\nTo run the full application:")
    print("1. Set GROQ_API_KEY environment variable (optional)")
    print("2. Install dependencies: pip install -r requirements.txt")
    print("3. Start backend: python backend/app.py")
    print("4. Open frontend/index.html in your browser")

if __name__ == '__main__':
    main()
