# 🎨 Color Extractor

A powerful web application that extracts dominant colors from images using K-means clustering with scikit-learn. Upload any image and get a beautiful color palette with RGB values, hex codes, and color percentages.

## ✨ Features

- **AI-Powered Color Extraction**: Uses K-means clustering to find the most dominant colors in any image
- **Multiple Output Formats**: Get colors in RGB, hex, and percentage formats
- **Batch Processing**: Extract colors from multiple images at once
- **Responsive Web Interface**: Beautiful, modern UI that works on desktop and mobile
- **Real-time Preview**: See your uploaded image and extracted colors instantly
- **Copy to Clipboard**: Click any color to copy its hex code
- **Customizable**: Choose how many colors to extract (1-20)

## 🚀 Quick Start

### Prerequisites

- Python 3.7+
- pip (Python package manager)

### Installation

1. **Clone or download this repository**
   ```bash
   git clone <repository-url>
   cd color-extractor
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the demo** (optional)
   ```bash
   python demo.py
   ```

4. **Start the backend server**
   ```bash
   cd backend
   python app.py
   ```
   The API will be available at `http://localhost:5000`

5. **Open the frontend**
   - Open `frontend/index.html` in your web browser
   - Or serve it with a local server for better performance

## 🎯 How It Works

### The Science Behind It

1. **Image Preprocessing**: Images are resized to 500x500 pixels for optimal processing speed while maintaining quality
2. **Pixel Extraction**: All pixels are converted to RGB values and flattened into a dataset
3. **K-Means Clustering**: The scikit-learn KMeans algorithm groups similar colors together
4. **Dominant Color Identification**: Cluster centers represent the most dominant colors
5. **Percentage Calculation**: Optional analysis shows how much of the image each color represents

### API Endpoints

#### `POST /extract-colors`
Extract dominant colors from a single image.

**Parameters:**
- `image` (file): Image file to process
- `n_colors` (int, optional): Number of colors to extract (default: 5, max: 20)
- `include_percentages` (bool, optional): Include color percentages (default: false)

**Response:**
```json
{
  "success": true,
  "colors": [
    {
      "rgb": [255, 0, 0],
      "hex": "#ff0000",
      "percentage": 25.5
    }
  ],
  "n_colors": 5,
  "include_percentages": true
}
```

#### `POST /extract-colors-batch`
Extract colors from multiple images at once.

**Parameters:**
- `images` (files): Multiple image files
- `n_colors` (int, optional): Number of colors per image (default: 5)

## 🛠️ Technical Details

### Backend Architecture

- **Framework**: Flask with CORS support
- **Image Processing**: Pillow (PIL) for image manipulation
- **Machine Learning**: scikit-learn for K-means clustering
- **Data Processing**: NumPy for efficient array operations

### Frontend Features

- **Drag & Drop**: Intuitive file upload interface
- **Real-time Preview**: Instant image preview and validation
- **Responsive Design**: Works on all screen sizes
- **Error Handling**: Comprehensive error messages and validation
- **Accessibility**: Keyboard navigation and screen reader support

### File Structure

```
color-extractor/
├── backend/
│   ├── app.py                 # Flask API server
│   ├── color_extractor.py     # Core color extraction logic
│   └── test_color_extractor.py # Unit tests
├── frontend/
│   └── index.html            # Web interface
├── requirements.txt          # Python dependencies
├── demo.py                  # Demo script
└── README.md               # This file
```

## 🧪 Testing

Run the test suite to validate functionality:

```bash
cd backend
python test_color_extractor.py
```

The tests cover:
- Image preprocessing and validation
- Color extraction accuracy
- RGB/hex conversion utilities
- Error handling for invalid inputs
- Percentage calculation accuracy

## 🎨 Usage Examples

### Basic Color Extraction

```python
from backend.color_extractor import ColorExtractor
from PIL import Image

# Initialize extractor
extractor = ColorExtractor()

# Load image
image = Image.open('your_image.jpg')

# Extract 5 dominant colors
colors = extractor.extract_colors(image, n_colors=5)

# Print results
for i, (r, g, b) in enumerate(colors, 1):
    hex_color = extractor.rgb_to_hex((r, g, b))
    print(f"Color {i}: RGB({r}, {g}, {b}) → {hex_color}")
```

### Color Extraction with Percentages

```python
# Extract colors with percentage information
color_data = extractor.get_color_percentages(image, n_colors=5)

for (r, g, b), percentage in color_data:
    hex_color = extractor.rgb_to_hex((r, g, b))
    print(f"RGB({r}, {g}, {b}) → {hex_color} ({percentage:.1f}%)")
```

## 🔧 Configuration

### Customizing Image Processing

You can customize the maximum image size for processing:

```python
# Process larger images (may be slower)
extractor = ColorExtractor(max_size=(800, 800))

# Process smaller images (faster)
extractor = ColorExtractor(max_size=(300, 300))
```

### API Configuration

Modify `backend/app.py` to change server settings:

```python
# Change port
app.run(debug=True, host='0.0.0.0', port=8000)

# Enable/disable debug mode
app.run(debug=False)
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is open source and available under the [MIT License](LICENSE).

## 🙏 Acknowledgments

- **scikit-learn** for the excellent K-means implementation
- **Pillow** for robust image processing capabilities
- **Flask** for the lightweight web framework
- **Color theory** research that inspired the clustering approach

## 🐛 Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Make sure you've installed all dependencies: `pip install -r requirements.txt`

2. **CORS errors in browser**
   - Ensure the Flask server is running with CORS enabled
   - Try serving the frontend with a local HTTP server

3. **Large image processing is slow**
   - Images are automatically resized, but very large images may still take time
   - Consider reducing the `max_size` parameter

4. **Colors don't match expectations**
   - K-means clustering finds statistically dominant colors, not necessarily the most visually prominent ones
   - Try adjusting the number of colors extracted
   - Very complex images may need more clusters for accurate representation

### Performance Tips

- For faster processing, use smaller images or reduce the `max_size` parameter
- Fewer colors (n_colors) will process faster
- JPEG images typically process faster than PNG due to compression

---

Made with ❤️ for the color-loving community!
