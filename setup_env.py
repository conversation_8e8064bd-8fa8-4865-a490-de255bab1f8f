"""
Environment setup script for Color Palette Generator
Helps users set up their Groq API key and test the system
"""

import os
import sys

def setup_groq_api_key():
    """Help user set up Groq API key"""
    print("🔑 Groq API Key Setup")
    print("=" * 30)
    
    current_key = os.getenv('GROQ_API_KEY')
    if current_key:
        print(f"✅ GROQ_API_KEY is already set: {current_key[:10]}...")
        return True
    
    print("❌ GROQ_API_KEY environment variable not found.")
    print("\nTo get full AI-powered text-to-color functionality, you need a Groq API key.")
    print("Don't worry - the system will work with fallback algorithms if you don't have one!")
    
    print("\n📋 How to get a Groq API key:")
    print("1. Go to https://console.groq.com/")
    print("2. Sign up for a free account")
    print("3. Navigate to API Keys section")
    print("4. Create a new API key")
    print("5. Copy the key")
    
    choice = input("\nDo you want to set your Groq API key now? (y/n): ").lower().strip()
    
    if choice == 'y':
        api_key = input("Enter your Groq API key: ").strip()
        if api_key:
            # Set for current session
            os.environ['GROQ_API_KEY'] = api_key
            
            # Provide instructions for permanent setup
            print("\n✅ API key set for this session!")
            print("\n💡 To make this permanent, add this to your system:")
            
            if sys.platform == "win32":
                print(f"   Windows: setx GROQ_API_KEY \"{api_key}\"")
                print("   Or add it to your system environment variables")
            else:
                print(f"   Linux/Mac: export GROQ_API_KEY=\"{api_key}\"")
                print("   Add this line to your ~/.bashrc or ~/.zshrc file")
            
            return True
        else:
            print("❌ No API key entered.")
    
    print("\n🔄 Continuing with fallback method (no AI, but still functional!)")
    return False

def check_dependencies():
    """Check if all required dependencies are installed"""
    print("\n📦 Checking Dependencies")
    print("=" * 25)
    
    required_packages = [
        'flask',
        'Pillow', 
        'scikit-learn',
        'numpy',
        'flask-cors'
    ]
    
    optional_packages = [
        'groq',
        'langchain',
        'langchain-groq'
    ]
    
    missing_required = []
    missing_optional = []
    
    # Check required packages
    for package in required_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_required.append(package)
    
    # Check optional packages
    for package in optional_packages:
        try:
            __import__(package.lower().replace('-', '_'))
            print(f"✅ {package} (optional)")
        except ImportError:
            print(f"⚠️  {package} (optional - needed for AI features)")
            missing_optional.append(package)
    
    if missing_required:
        print(f"\n❌ Missing required packages: {', '.join(missing_required)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    if missing_optional:
        print(f"\n⚠️  Missing optional packages: {', '.join(missing_optional)}")
        print("AI text-to-color will use fallback method")
    
    print("\n✅ All required dependencies are installed!")
    return True

def test_system():
    """Run basic system tests"""
    print("\n🧪 Testing System Components")
    print("=" * 30)
    
    try:
        # Test color extraction
        sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))
        from backend.color_extractor import ColorExtractor
        
        extractor = ColorExtractor()
        print("✅ Color extraction module loaded")
        
        # Test palette generation
        from backend.generate_color import generate_smart_palette
        palette = generate_smart_palette("#FF6B35", 5)
        print(f"✅ Palette generation works: {len(palette)} colors")
        
        # Test text-to-color fallback
        from backend.text_to_color import TextToColorService
        temp_service = TextToColorService.__new__(TextToColorService)
        color = temp_service._fallback_color_extraction("tropical sunset")
        print(f"✅ Text-to-color fallback works: {color}")
        
        # Test AI service if possible
        try:
            service = TextToColorService()
            print("✅ AI text-to-color service initialized")
        except ValueError:
            print("⚠️  AI service not available (using fallback)")
        
        print("\n🎉 All system components are working!")
        return True
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🎨 Color Palette Generator - Environment Setup")
    print("=" * 55)
    
    print("This script will help you set up the Color Palette Generator system.")
    print("The system works in two modes:")
    print("1. 🤖 AI-Powered: Uses Groq's LLaMA model for intelligent color extraction")
    print("2. 🎯 Fallback: Uses predefined algorithms (still very effective!)")
    
    # Step 1: Check dependencies
    if not check_dependencies():
        print("\n❌ Please install missing dependencies first:")
        print("   pip install -r requirements.txt")
        return
    
    # Step 2: Setup API key
    has_api_key = setup_groq_api_key()
    
    # Step 3: Test system
    if not test_system():
        print("\n❌ System test failed. Please check your installation.")
        return
    
    # Step 4: Provide next steps
    print("\n🚀 Setup Complete! Next Steps:")
    print("=" * 35)
    
    if has_api_key:
        print("✅ You're all set with AI-powered features!")
    else:
        print("⚠️  Running with fallback algorithms (still great!)")
    
    print("\nTo start the application:")
    print("1. 🖥️  Start the backend:")
    print("   cd backend && python app.py")
    print("\n2. 🌐 Open the frontend:")
    print("   Open frontend/index.html in your web browser")
    print("\n3. 🎨 Start creating beautiful color palettes!")
    
    print("\n📚 Usage Tips:")
    print("• Try descriptions like 'tropical sunset' or 'elegant luxury brand'")
    print("• Experiment with different palette modes (analogous, triadic, etc.)")
    print("• Upload images to extract their dominant colors")
    print("• Click any color to copy its hex code to clipboard")
    
    print("\n🆘 Need help? Check the README.md file for detailed instructions!")

if __name__ == '__main__':
    main()
