Image Processing: OpenCV or Pillow (PIL) for handling image uploads and initial processing.

AI/ML Models:

Text Understanding: Sentence-Transformers (a library using models like BERT) to convert the user's text prompt into a meaningful vector (embedding).

Image Color Extraction: Scikit-learn to perform K-Means clustering on the image's pixel data. This is a very effective and fast way to find the dominant colors.

Generative Color Engine: While a GAN or diffusion model is cool, it's very complex for a hackathon. A smarter approach is to use programmatic color theory. Once you have a base color (from text or image), you can generate a harmonious palette using algorithms for complementary, analogous, triadic, or tetradic color schemes.

Accessibility Logic: You can write this from scratch in Python. The formulas are well-defined

Core Feature Implementation Guide
1. Multi-Modal Prompting
A. From Image:

Upload: User uploads an image to the frontend.

Backend Processing: The image is sent to the backend. Use Pillow to resize it to a manageable size (e.g., 500x500 pixels) to speed up processing.

K-Means Clustering:

Convert the image's pixels into a list of RGB values.

Use sklearn.cluster.KMeans to cluster these RGB values into k groups (where k is the number of colors you want, e.g., 5).

The center of each cluster will be a dominant color in the image. This becomes your initial palette.

B. From Text:

Prompt: User enters a text prompt like "An energetic palette for a fitness brand inspired by a tropical sunset."

Embedding: Use a Sentence-Transformers model to convert this text into a numerical vector (embedding).

Text-to-Color Mapping: This is the trickiest part. For a hackathon, you can pre-define a mapping. Create a small dataset of color names/concepts (e.g., "ocean blue," "forest green," "sunset orange") and their corresponding HEX codes. Convert these names into embeddings as well. When a user prompt comes in, find the closest matching color embedding from your pre-defined set using cosine similarity. This gives you a starting "seed" color.

Palette Generation: Once you have the seed color, use color theory algorithms to generate the other 4-6 colors for the palette

2. Automated Accessibility Auditing
Color-Blindness Simulation:

You can simulate common forms of color blindness (like Deuteranopia, Protanopia) by applying a specific transformation matrix to the RGB values of your palette. For example, to simulate Deuteranopia, you would multiply a color's [R, G, B] vector by a predefined 3x3 matrix. You can find these matrices online easily. Implement a function that takes a palette and returns versions for different simulations.