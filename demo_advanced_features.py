"""
Advanced Features Demo for Color Palette Generator
Demonstrates palette evolution and color explanation capabilities
"""

import sys
import os
import json

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

def demo_palette_evolution():
    """Demonstrate palette evolution feature"""
    print("🧬 Palette Evolution Demo")
    print("=" * 40)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ Using AI-powered evolution")
        except ValueError:
            service = TextToColorService.__new__(TextToColorService)
            print("⚠️  Using fallback evolution methods")
        
        # Demo scenarios
        scenarios = [
            {
                'name': 'Corporate Rebranding',
                'original': ['#FF6B35', '#F7931E', '#FFD23F', '#06FFA5', '#118AB2'],
                'feedback': 'Transform this energetic palette into something more professional and trustworthy for a financial services company',
                'description': 'Energetic startup → Professional finance'
            },
            {
                'name': 'Seasonal Adaptation',
                'original': ['#E74C3C', '#C0392B', '#A93226', '#922B21', '#7B241C'],
                'feedback': 'Make this warm red palette cooler and more suitable for a winter theme',
                'description': 'Warm reds → Cool winter theme'
            },
            {
                'name': 'Accessibility Enhancement',
                'original': ['#FFEB3B', '#FFC107', '#FF9800', '#FF5722', '#F44336'],
                'feedback': 'Increase contrast and make these colors more accessible for users with visual impairments',
                'description': 'Low contrast → High accessibility'
            }
        ]
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n🎯 Scenario {i}: {scenario['name']}")
            print(f"   Description: {scenario['description']}")
            print(f"   Original Palette: {', '.join(scenario['original'])}")
            print(f"   User Feedback: {scenario['feedback']}")
            
            # Evolve the palette
            if hasattr(service, 'evolve_palette'):
                result = service.evolve_palette(scenario['original'], scenario['feedback'])
            else:
                result = service._fallback_palette_evolution(scenario['original'], scenario['feedback'])
            
            if result['success']:
                print(f"   ✨ Evolved Palette: {', '.join(result['evolved_palette'])}")
                print(f"   📝 Changes Made: {result['changes_made']}")
                print(f"   🧠 Reasoning: {result['reasoning']}")
                print(f"   🔧 Method: {result['method']}")
            else:
                print(f"   ❌ Evolution failed: {result.get('error', 'Unknown error')}")
        
        print("\n✅ Palette evolution demo completed!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def demo_color_explanation():
    """Demonstrate color explanation feature"""
    print("\n🎨 Color Psychology & Analysis Demo")
    print("=" * 45)
    
    try:
        from backend.text_to_color import TextToColorService
        
        # Initialize service
        try:
            service = TextToColorService()
            print("✅ Using AI-powered explanations")
        except ValueError:
            service = TextToColorService.__new__(TextToColorService)
            print("⚠️  Using fallback explanation methods")
        
        # Demo palettes with different contexts
        palettes = [
            {
                'name': 'Healthcare App',
                'colors': ['#2ECC71', '#27AE60', '#A9DFBF', '#D5F4E6', '#FFFFFF'],
                'context': 'Mobile health and wellness application for stress management',
                'theme': 'Calming and trustworthy medical interface'
            },
            {
                'name': 'Luxury Brand',
                'colors': ['#8E44AD', '#9B59B6', '#D7BDE2', '#F4ECF7', '#1C1C1C'],
                'context': 'High-end jewelry and fashion brand targeting affluent customers',
                'theme': 'Sophisticated and exclusive luxury experience'
            },
            {
                'name': 'Tech Startup',
                'colors': ['#3498DB', '#2980B9', '#AED6F1', '#EBF5FB', '#2C3E50'],
                'context': 'Innovative AI technology platform for business automation',
                'theme': 'Modern, trustworthy, and cutting-edge technology'
            }
        ]
        
        for i, palette_info in enumerate(palettes, 1):
            print(f"\n🎯 Analysis {i}: {palette_info['name']}")
            print(f"   Theme: {palette_info['theme']}")
            print(f"   Colors: {', '.join(palette_info['colors'])}")
            print(f"   Context: {palette_info['context']}")
            
            # Get color explanations
            if hasattr(service, 'explain_colors'):
                result = service.explain_colors(palette_info['colors'], palette_info['context'])
            else:
                result = service._fallback_color_explanation(palette_info['colors'], palette_info['context'])
            
            if result['success']:
                analysis = result['palette_analysis']
                print(f"\n   📊 Overall Analysis:")
                print(f"      Mood: {analysis['overall_mood']}")
                print(f"      Harmony: {analysis['harmony_type']}")
                print(f"      Applications: {', '.join(analysis['applications'])}")
                print(f"      UX Impact: {analysis['user_experience']}")
                
                print(f"\n   🎨 Color Breakdown:")
                for color_exp in result['color_explanations'][:3]:  # Show first 3 colors
                    print(f"      {color_exp['color']} ({color_exp['name']}):")
                    print(f"         Psychology: {color_exp['psychology']}")
                    print(f"         Design Use: {color_exp['design_use']}")
                
                print(f"   🔧 Method: {result['method']}")
            else:
                print(f"   ❌ Analysis failed: {result.get('error', 'Unknown error')}")
        
        print("\n✅ Color explanation demo completed!")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def demo_complete_workflow():
    """Demonstrate the complete workflow"""
    print("\n🔄 Complete Workflow Demo")
    print("=" * 35)
    
    try:
        from backend.text_to_color import TextToColorService
        from backend.generate_color import generate_smart_palette
        
        # Initialize service
        try:
            service = TextToColorService()
            ai_available = True
            print("✅ Using full AI-powered workflow")
        except ValueError:
            service = TextToColorService.__new__(TextToColorService)
            ai_available = False
            print("⚠️  Using fallback workflow methods")
        
        # Workflow scenario
        description = "Modern eco-friendly coffee shop with warm, welcoming atmosphere"
        print(f"\n🎯 Scenario: {description}")
        
        # Step 1: Generate initial palette
        print("\n📝 Step 1: Generating initial palette...")
        if ai_available and hasattr(service, 'generate_palette_from_text'):
            initial_result = service.generate_palette_from_text(description, 'smart', 5)
            initial_palette = initial_result['palette']
            seed_color = initial_result['seed_color']
        else:
            seed_color = service._fallback_color_extraction(description)
            initial_palette = generate_smart_palette(seed_color, 5)
        
        print(f"   Seed Color: {seed_color}")
        print(f"   Initial Palette: {', '.join(initial_palette)}")
        
        # Step 2: Evolve the palette
        print("\n🧬 Step 2: Evolving palette based on feedback...")
        feedback = "Make it warmer and more inviting, with earthy tones"
        
        if ai_available and hasattr(service, 'evolve_palette'):
            evolution_result = service.evolve_palette(initial_palette, feedback)
        else:
            evolution_result = service._fallback_palette_evolution(initial_palette, feedback)
        
        if evolution_result['success']:
            evolved_palette = evolution_result['evolved_palette']
            print(f"   Feedback: {feedback}")
            print(f"   Evolved Palette: {', '.join(evolved_palette)}")
            print(f"   Changes: {evolution_result['changes_made']}")
        else:
            evolved_palette = initial_palette
            print(f"   ❌ Evolution failed, using original palette")
        
        # Step 3: Explain the final palette
        print("\n🎨 Step 3: Analyzing final palette...")
        
        if ai_available and hasattr(service, 'explain_colors'):
            explanation_result = service.explain_colors(evolved_palette, description)
        else:
            explanation_result = service._fallback_color_explanation(evolved_palette, description)
        
        if explanation_result['success']:
            analysis = explanation_result['palette_analysis']
            print(f"   Overall Mood: {analysis['overall_mood']}")
            print(f"   Best Applications: {', '.join(analysis['applications'])}")
            print(f"   UX Impact: {analysis['user_experience']}")
            
            # Show one color example
            if explanation_result['color_explanations']:
                example = explanation_result['color_explanations'][0]
                print(f"   Example - {example['color']}: {example['psychology']}")
        else:
            print(f"   ❌ Analysis failed")
        
        print("\n✅ Complete workflow demo finished!")
        print("\n💡 This workflow shows how you can:")
        print("   1. Generate palettes from natural language")
        print("   2. Refine them based on feedback")
        print("   3. Understand their psychological impact")
        print("   4. Make informed design decisions")
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")

def main():
    """Run all advanced feature demos"""
    print("🚀 Advanced Color Palette Generator Features Demo")
    print("=" * 60)
    
    print("This demo showcases the new AI-powered features:")
    print("🧬 Palette Evolution - Refine palettes with natural language feedback")
    print("🎨 Color Psychology - Understand the impact of your color choices")
    print("🔄 Complete Workflow - End-to-end palette creation and analysis")
    
    # Run demos
    demo_palette_evolution()
    demo_color_explanation()
    demo_complete_workflow()
    
    print("\n🎉 All demos completed!")
    print("\n🔗 Next Steps:")
    print("1. Start the backend: python backend/app.py")
    print("2. Open frontend/index.html in your browser")
    print("3. Try the new evolution and explanation features")
    print("4. Experiment with different feedback and contexts")
    
    print("\n💡 Pro Tips:")
    print("• Be specific in your evolution feedback: 'warmer', 'more professional', 'higher contrast'")
    print("• Provide context for better explanations: 'healthcare app', 'luxury brand', 'children's toy'")
    print("• Use the workflow endpoint for complete automation")
    print("• All features work with fallback algorithms even without Groq API key")

if __name__ == '__main__':
    main()
