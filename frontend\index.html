<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Color Extractor - Extract Dominant Colors from Images</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            padding: 40px;
        }

        .mode-selector {
            display: flex;
            justify-content: center;
            margin-bottom: 30px;
            gap: 10px;
        }

        .mode-btn {
            background: #f8f9ff;
            border: 2px solid #667eea;
            color: #667eea;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 600;
        }

        .mode-btn.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .mode-btn:hover {
            transform: translateY(-2px);
        }

        .section {
            display: none;
        }

        .section.active {
            display: block;
        }

        .text-input-area {
            margin: 20px 0;
        }

        .text-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            font-size: 1.1em;
            resize: vertical;
            min-height: 100px;
            font-family: inherit;
        }

        .text-input:focus {
            outline: none;
            border-color: #667eea;
        }

        .palette-mode-selector {
            margin: 20px 0;
            text-align: center;
        }

        .palette-mode-selector select {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
            background: white;
        }

        .upload-section {
            text-align: center;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 40px;
            margin: 20px 0;
            background: #f8f9ff;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: #f0f2ff;
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: #e8ebff;
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3em;
            color: #667eea;
            margin-bottom: 20px;
        }

        .file-input {
            display: none;
        }

        .upload-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 1.1em;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .upload-btn:hover {
            transform: translateY(-2px);
        }

        .controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }

        .control-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 5px;
        }

        .control-group label {
            font-weight: 600;
            color: #333;
        }

        .control-group input, .control-group select {
            padding: 8px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1em;
        }

        .extract-btn {
            background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
            color: white;
            border: none;
            padding: 15px 40px;
            border-radius: 25px;
            font-size: 1.2em;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 20px 0;
        }

        .extract-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(17, 153, 142, 0.3);
        }

        .extract-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .results {
            margin-top: 40px;
        }

        .image-preview {
            text-align: center;
            margin-bottom: 30px;
        }

        .preview-img {
            max-width: 100%;
            max-height: 300px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .color-palette {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .color-card {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
            transition: transform 0.3s ease;
        }

        .color-card:hover {
            transform: translateY(-5px);
        }

        .color-swatch {
            width: 100%;
            height: 80px;
            border-radius: 10px;
            margin-bottom: 15px;
            border: 3px solid #f0f0f0;
            cursor: pointer;
            transition: transform 0.3s ease;
        }

        .color-swatch:hover {
            transform: scale(1.05);
        }

        .color-info {
            font-family: 'Courier New', monospace;
        }

        .color-hex {
            font-size: 1.2em;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }

        .color-rgb {
            color: #666;
            font-size: 0.9em;
        }

        .color-percentage {
            color: #667eea;
            font-weight: bold;
            margin-top: 5px;
        }

        .error {
            background: #ffe6e6;
            color: #d63031;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #d63031;
        }

        .success {
            background: #e6ffe6;
            color: #00b894;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            border-left: 4px solid #00b894;
        }

        @media (max-width: 768px) {
            .controls {
                flex-direction: column;
                align-items: center;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .main-content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎨 Color Palette Generator</h1>
            <p>Extract colors from images or generate palettes from text descriptions using AI</p>
        </div>

        <div class="main-content">
            <!-- Mode Selector -->
            <div class="mode-selector">
                <button class="mode-btn active" onclick="switchMode('image')">📸 From Image</button>
                <button class="mode-btn" onclick="switchMode('text')">✍️ From Text</button>
            </div>

            <!-- Image Mode Section -->
            <div class="section active" id="imageSection">
                <div class="upload-section">
                    <div class="upload-area" id="uploadArea">
                        <div class="upload-icon">📸</div>
                        <h3>Drop your image here or click to browse</h3>
                        <p>Supports JPEG, PNG, GIF, and more</p>
                        <input type="file" id="imageInput" class="file-input" accept="image/*">
                        <button class="upload-btn" onclick="document.getElementById('imageInput').click()">
                            Choose Image
                        </button>
                    </div>

                    <div class="controls">
                        <div class="control-group">
                            <label for="colorCount">Number of Colors</label>
                            <input type="number" id="colorCount" min="1" max="20" value="5">
                        </div>
                        <div class="control-group">
                            <label for="includePercentages">Show Percentages</label>
                            <select id="includePercentages">
                                <option value="false">No</option>
                                <option value="true">Yes</option>
                            </select>
                        </div>
                    </div>

                    <button class="extract-btn" id="extractBtn" onclick="extractColors()" disabled>
                        Extract Colors
                    </button>
                </div>
            </div>

            <!-- Text Mode Section -->
            <div class="section" id="textSection">
                <div class="text-input-area">
                    <h3>Describe your desired color palette</h3>
                    <textarea
                        id="textInput"
                        class="text-input"
                        placeholder="Describe the theme, mood, or style you want...

Examples:
• 'Tropical sunset beach vacation with warm oranges and corals'
• 'Elegant luxury brand for high-end jewelry with sophisticated tones'
• 'Energetic fitness app for young athletes with vibrant colors'
• 'Calm meditation and wellness center with peaceful blues'
• 'Modern tech startup with innovation and trust themes'"></textarea>
                </div>

                <div class="controls">
                    <div class="control-group">
                        <label for="textColorCount">Number of Colors</label>
                        <input type="number" id="textColorCount" min="1" max="20" value="5">
                    </div>
                    <div class="control-group">
                        <label for="paletteMode">Palette Style</label>
                        <select id="paletteMode">
                            <option value="smart">Smart (Recommended)</option>
                            <option value="analogous">Analogous (Similar hues)</option>
                            <option value="split_complementary">Split Complementary</option>
                            <option value="triadic">Triadic (3-color harmony)</option>
                            <option value="tetradic">Tetradic (4-color harmony)</option>
                        </select>
                    </div>
                </div>

                <button class="extract-btn" id="generateBtn" onclick="generateFromText()">
                    Generate Palette
                </button>
            </div>

            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Extracting colors from your image...</p>
            </div>

            <div class="results" id="results" style="display: none;">
                <div class="image-preview" id="imagePreview"></div>
                <div class="color-palette" id="colorPalette"></div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let selectedFile = null;
        let currentMode = 'image';
        const API_BASE_URL = 'http://localhost:5000';

        // DOM elements
        const uploadArea = document.getElementById('uploadArea');
        const imageInput = document.getElementById('imageInput');
        const extractBtn = document.getElementById('extractBtn');
        const generateBtn = document.getElementById('generateBtn');
        const textInput = document.getElementById('textInput');
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        const imagePreview = document.getElementById('imagePreview');
        const colorPalette = document.getElementById('colorPalette');

        // Event listeners
        imageInput.addEventListener('change', handleFileSelect);
        uploadArea.addEventListener('click', () => imageInput.click());
        uploadArea.addEventListener('dragover', handleDragOver);
        uploadArea.addEventListener('dragleave', handleDragLeave);
        uploadArea.addEventListener('drop', handleDrop);
        textInput.addEventListener('input', handleTextInput);

        function switchMode(mode) {
            currentMode = mode;

            // Update mode buttons
            document.querySelectorAll('.mode-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // Show/hide sections
            document.querySelectorAll('.section').forEach(section => section.classList.remove('active'));
            if (mode === 'image') {
                document.getElementById('imageSection').classList.add('active');
            } else {
                document.getElementById('textSection').classList.add('active');
            }

            // Clear results
            results.style.display = 'none';
        }

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                selectedFile = file;
                showImagePreview(file);
                extractBtn.disabled = false;
            }
        }

        function handleTextInput(event) {
            const text = event.target.value.trim();
            generateBtn.disabled = text.length === 0;
        }

        function handleDragOver(event) {
            event.preventDefault();
            uploadArea.classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            uploadArea.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];
                if (file.type.startsWith('image/')) {
                    selectedFile = file;
                    imageInput.files = files;
                    showImagePreview(file);
                    extractBtn.disabled = false;
                }
            }
        }

        function showImagePreview(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                imagePreview.innerHTML = `
                    <img src="${e.target.result}" alt="Preview" class="preview-img">
                    <p><strong>File:</strong> ${file.name}</p>
                    <p><strong>Size:</strong> ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
                `;
            };
            reader.readAsDataURL(file);
        }

        async function extractColors() {
            if (!selectedFile) {
                showError('Please select an image first');
                return;
            }

            const colorCount = document.getElementById('colorCount').value;
            const includePercentages = document.getElementById('includePercentages').value;

            // Show loading
            loading.style.display = 'block';
            results.style.display = 'none';
            extractBtn.disabled = true;

            try {
                const formData = new FormData();
                formData.append('image', selectedFile);
                formData.append('n_colors', colorCount);
                formData.append('include_percentages', includePercentages);

                const response = await fetch(`${API_BASE_URL}/extract-colors`, {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.success) {
                    displayResults(data);
                    showSuccess(`Successfully extracted ${data.n_colors} colors!`);
                } else {
                    showError(data.message || 'Failed to extract colors');
                }
            } catch (error) {
                console.error('Error:', error);
                showError('Failed to connect to the server. Make sure the backend is running.');
            } finally {
                loading.style.display = 'none';
                extractBtn.disabled = false;
            }
        }

        function displayResults(data) {
            colorPalette.innerHTML = '';

            data.colors.forEach((color, index) => {
                const colorCard = document.createElement('div');
                colorCard.className = 'color-card';

                const percentageHtml = color.percentage !== undefined
                    ? `<div class="color-percentage">${color.percentage}%</div>`
                    : '';

                colorCard.innerHTML = `
                    <div class="color-swatch"
                         style="background-color: ${color.hex};"
                         onclick="copyToClipboard('${color.hex}')">
                    </div>
                    <div class="color-info">
                        <div class="color-hex">${color.hex}</div>
                        <div class="color-rgb">RGB(${color.rgb.join(', ')})</div>
                        ${percentageHtml}
                    </div>
                `;

                colorPalette.appendChild(colorCard);
            });

            results.style.display = 'block';
        }

        async function generateFromText() {
            const textDescription = textInput.value.trim();
            if (!textDescription) {
                showError('Please enter a description for your color palette');
                return;
            }

            const colorCount = document.getElementById('textColorCount').value;
            const paletteMode = document.getElementById('paletteMode').value;

            // Show loading
            loading.style.display = 'block';
            results.style.display = 'none';
            generateBtn.disabled = true;

            try {
                const response = await fetch(`${API_BASE_URL}/generate-palette-from-text`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        text_description: textDescription,
                        palette_mode: paletteMode,
                        num_colors: parseInt(colorCount)
                    })
                });

                const data = await response.json();

                if (data.success) {
                    displayTextResults(data);
                    showSuccess(`Successfully generated ${data.num_colors} colors from your description!`);
                } else {
                    showError(data.message || 'Failed to generate palette');
                }
            } catch (error) {
                console.error('Error:', error);
                showError('Failed to connect to the server. Make sure the backend is running.');
            } finally {
                loading.style.display = 'none';
                generateBtn.disabled = false;
            }
        }

        function displayTextResults(data) {
            // Clear previous results
            imagePreview.innerHTML = '';
            colorPalette.innerHTML = '';

            // Show text description and seed color
            imagePreview.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <h3>Generated from: "${data.text_description}"</h3>
                    <p><strong>Seed Color:</strong> ${data.seed_color}</p>
                    <p><strong>Palette Mode:</strong> ${data.palette_mode}</p>
                    <p><strong>Method:</strong> ${data.method === 'groq_llama' ? '🤖 AI-Powered' : '🎯 Fallback Algorithm'}</p>
                    <div style="width: 60px; height: 60px; background-color: ${data.seed_color}; margin: 10px auto; border-radius: 50%; border: 3px solid #ddd;"></div>
                </div>
            `;

            // Display palette
            data.palette.forEach((color, index) => {
                const colorCard = document.createElement('div');
                colorCard.className = 'color-card';

                colorCard.innerHTML = `
                    <div class="color-swatch"
                         style="background-color: ${color.hex};"
                         onclick="copyToClipboard('${color.hex}')">
                    </div>
                    <div class="color-info">
                        <div class="color-hex">${color.hex}</div>
                        <div class="color-rgb">RGB(${color.rgb.join(', ')})</div>
                        ${index === 0 ? '<div class="color-percentage">Seed Color</div>' : ''}
                    </div>
                `;

                colorPalette.appendChild(colorCard);
            });

            results.style.display = 'block';
        }

        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showSuccess(`Copied ${text} to clipboard!`);
            }).catch(() => {
                showError('Failed to copy to clipboard');
            });
        }

        function showError(message) {
            const existingError = document.querySelector('.error');
            if (existingError) existingError.remove();

            const errorDiv = document.createElement('div');
            errorDiv.className = 'error';
            errorDiv.textContent = message;

            document.querySelector('.main-content').insertBefore(errorDiv, document.querySelector('.upload-section'));

            setTimeout(() => errorDiv.remove(), 5000);
        }

        function showSuccess(message) {
            const existingSuccess = document.querySelector('.success');
            if (existingSuccess) existingSuccess.remove();

            const successDiv = document.createElement('div');
            successDiv.className = 'success';
            successDiv.textContent = message;

            document.querySelector('.main-content').insertBefore(successDiv, document.querySelector('.upload-section'));

            setTimeout(() => successDiv.remove(), 3000);
        }
    </script>
</body>
</html>
